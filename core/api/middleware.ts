import { NextRequest } from "next/server";
import { auth } from "@/core/auth/server";
import { apiUnauthorized, apiForbidden, apiInternalError } from "./response";
import { ApiRequest } from "./types";
import { UserRole, isInternalRole, isClientRole } from "@/core/auth/roles";
import { Permission, hasPermission } from "@/core/auth/permissions";
import { getDb } from "@/core/database";
import { users, userOrganizations } from "@/core/database/schema";
import { eq, and } from "drizzle-orm";

interface AuthOptions {
  requireUserType?: "internal" | "client";
  requireOrganization?: boolean;
  requirePermissions?: Permission[];
  requireAnyPermission?: Permission[];
  requireRole?: UserRole;
  allowPublic?: boolean;
}

export async function withApiAuth(
  handler: (request: NextRequest, context: ApiRequest) => Promise<Response>,
  options: AuthOptions = {}
) {
  return async (request: NextRequest) => {
    try {
      // Allow public access if specified
      if (options.allowPublic) {
        const context: ApiRequest = {
          user: {
            id: "anonymous",
            email: "<EMAIL>",
            userType: "internal",
            organizationId: undefined,
            clientId: undefined,
            roles: [],
            permissions: [],
          },
          organizationId: "",
        };
        return await handler(request, context);
      }

      const session = await auth.api.getSession({
        headers: request.headers,
      });

      if (!session) {
        return apiUnauthorized();
      }

      const { user } = session;

      // Get user details from database
      const db = getDb();
      const userRecord = await db
        .select()
        .from(users)
        .where(eq(users.id, user.id))
        .limit(1);

      if (!userRecord.length || !userRecord[0].isActive) {
        return apiUnauthorized("User account is inactive");
      }

      const userData = userRecord[0];

      // Get user roles and permissions from database
      const userRoles = await getUserRoles(user.id, userData.organizationId);
      const userPermissions = await getUserPermissions(user.id, userRoles);

      // Check user type requirement
      if (options.requireUserType) {
        const hasCorrectUserType = userRoles.some((role) => {
          if (options.requireUserType === "internal") {
            return isInternalRole(role);
          } else {
            return isClientRole(role);
          }
        });

        if (!hasCorrectUserType) {
          return apiForbidden("Invalid user type for this endpoint");
        }
      }

      // Check organization requirement
      if (options.requireOrganization && !userData.organizationId) {
        return apiForbidden("Organization membership required");
      }

      // Check role requirement
      if (options.requireRole) {
        if (!userRoles.includes(options.requireRole)) {
          return apiForbidden(`Role required: ${options.requireRole}`);
        }
      }

      // Check permission requirements
      if (options.requirePermissions?.length) {
        const hasAllPermissions = options.requirePermissions.every(
          (permission) =>
            userRoles.some((role) =>
              hasPermission(role, permission, userPermissions)
            )
        );

        if (!hasAllPermissions) {
          return apiForbidden("Insufficient permissions");
        }
      }

      // Check any permission requirement
      if (options.requireAnyPermission?.length) {
        const hasAnyRequiredPermission = options.requireAnyPermission.some(
          (permission) =>
            userRoles.some((role) =>
              hasPermission(role, permission, userPermissions)
            )
        );

        if (!hasAnyRequiredPermission) {
          return apiForbidden("Insufficient permissions");
        }
      }

      const context: ApiRequest = {
        user: {
          id: user.id,
          email: user.email,
          userType: userData.userType as "internal" | "client",
          organizationId: userData.organizationId,
          clientId: userData.clientId,
          roles: userRoles,
          permissions: userPermissions,
        },
        organizationId: userData.organizationId || "",
      };

      return await handler(request, context);
    } catch (error) {
      console.error("API Auth Error:", error);
      return apiInternalError("Authentication failed");
    }
  };
}

// Helper functions to get user roles and permissions
async function getUserRoles(
  userId: string,
  organizationId?: string
): Promise<UserRole[]> {
  const db = getDb();
  
  if (!organizationId) {
    // Return default role for users without organization
    return ["organization_member" as UserRole];
  }

  try {
    // Get user's role in the organization
    const userOrgRecord = await db
      .select()
      .from(userOrganizations)
      .where(
        and(
          eq(userOrganizations.userId, userId),
          eq(userOrganizations.organizationId, organizationId),
          eq(userOrganizations.isActive, true)
        )
      )
      .limit(1);

    if (userOrgRecord.length) {
      return [userOrgRecord[0].role as UserRole];
    }

    return ["organization_member" as UserRole]; // Default role
  } catch (error) {
    console.error("Error fetching user roles:", error);
    return ["organization_member" as UserRole]; // Fallback
  }
}

async function getUserPermissions(
  userId: string,
  roles: UserRole[]
): Promise<Permission[]> {
  // For now, return empty array as custom permissions are not implemented
  // This would query a user_permissions table if custom permissions are needed
  return [];
}

// Convenience middleware functions
export function withInternalAuth(
  handler: (request: NextRequest, context: ApiRequest) => Promise<Response>,
  options: Omit<AuthOptions, "requireUserType"> = {}
) {
  return withApiAuth(handler, { ...options, requireUserType: "internal" });
}

export function withClientAuth(
  handler: (request: NextRequest, context: ApiRequest) => Promise<Response>,
  options: Omit<AuthOptions, "requireUserType"> = {}
) {
  return withApiAuth(handler, { ...options, requireUserType: "client" });
}

export function withPermissions(
  permissions: Permission[],
  handler: (request: NextRequest, context: ApiRequest) => Promise<Response>,
  options: Omit<AuthOptions, "requirePermissions"> = {}
) {
  return withApiAuth(handler, { ...options, requirePermissions: permissions });
}
