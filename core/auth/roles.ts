export const USER_ROLES = {
  // Internal user roles (freelancers, team members)
  SUPER_ADMIN: "super_admin",
  ORGANIZATION_OWNER: "organization_owner",
  ORGANIZATION_ADMIN: "organization_admin",
  ORGANIZATION_MEMBER: "organization_member",
  ORGANIZATION_VIEWER: "organization_viewer",

  // Client user roles (client portal access)
  CLIENT_ADMIN: "client_admin",
  CLIENT_USER: "client_user",
  CLIENT_VIEWER: "client_viewer",
} as const;

export type UserRole = (typeof USER_ROLES)[keyof typeof USER_ROLES];

export interface RoleDefinition {
  name: string;
  description: string;
  userType: "internal" | "client";
  level: number; // Higher number = more permissions
  inherits?: UserRole[];
}

export const ROLE_DEFINITIONS: Record<UserRole, RoleDefinition> = {
  [USER_ROLES.SUPER_ADMIN]: {
    name: "Super Admin",
    description: "Full system access across all organizations",
    userType: "internal",
    level: 100,
  },

  [USER_ROLES.ORGANIZATION_OWNER]: {
    name: "Organization Owner",
    description: "Full access to organization and all its data",
    userType: "internal",
    level: 90,
  },

  [USER_ROLES.ORGANIZATION_ADMIN]: {
    name: "Organization Admin",
    description: "Administrative access to organization features",
    userType: "internal",
    level: 80,
  },

  [USER_ROLES.ORGANIZATION_MEMBER]: {
    name: "Organization Member",
    description: "Standard member access to organization features",
    userType: "internal",
    level: 60,
  },

  [USER_ROLES.ORGANIZATION_VIEWER]: {
    name: "Organization Viewer",
    description: "Read-only access to organization data",
    userType: "internal",
    level: 40,
  },

  [USER_ROLES.CLIENT_ADMIN]: {
    name: "Client Admin",
    description: "Administrative access to client portal features",
    userType: "client",
    level: 70,
  },

  [USER_ROLES.CLIENT_USER]: {
    name: "Client User",
    description: "Standard access to client portal features",
    userType: "client",
    level: 50,
  },

  [USER_ROLES.CLIENT_VIEWER]: {
    name: "Client Viewer",
    description: "Read-only access to client portal",
    userType: "client",
    level: 30,
  },
};

// Helper functions
export function isInternalRole(role: UserRole): boolean {
  return ROLE_DEFINITIONS[role].userType === "internal";
}

export function isClientRole(role: UserRole): boolean {
  return ROLE_DEFINITIONS[role].userType === "client";
}

export function hasHigherRole(
  userRole: UserRole,
  requiredRole: UserRole
): boolean {
  return (
    ROLE_DEFINITIONS[userRole].level >= ROLE_DEFINITIONS[requiredRole].level
  );
}

export function getRolesByUserType(
  userType: "internal" | "client"
): UserRole[] {
  return Object.keys(ROLE_DEFINITIONS).filter(
    (role) => ROLE_DEFINITIONS[role as UserRole].userType === userType
  ) as UserRole[];
}

export function getInternalRoles(): UserRole[] {
  return getRolesByUserType("internal");
}

export function getClientRoles(): UserRole[] {
  return getRolesByUserType("client");
}
