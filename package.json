{"name": "freelancer-hub", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy", "preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "cf:type": "wrangler types --env-interface CloudflareEnv ./cloudflare-env.d.ts", "lint:all": "pnpm exec biome check --write"}, "dependencies": {"@neondatabase/serverless": "^1.0.1", "@opennextjs/cloudflare": "^1.5.1", "@reduxjs/toolkit": "^2.8.2", "better-auth": "^1.3.0", "drizzle-orm": "^0.44.3", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-redux": "^9.2.0", "zod": "^4.0.5"}, "devDependencies": {"@biomejs/biome": "2.1.2", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.9", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.4", "tailwindcss": "^4", "typescript": "^5", "wrangler": "^4.25.0"}}